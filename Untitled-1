<?php
    // Get pagination data
    $modelName = $this->viewVars['modelName'] ?? null;
    $pagination = null;
    $currentPage = 1;
    $totalPages = 1;
    $urlParams = $this->params['url'];
    $nextPage = false;
    $prevPage = false;
    $startItem = 1;
    $endItem = 0;
    if ($modelName && isset($this->params['paging'][$modelName])) {
        $pagination = $this->params['paging'][$modelName];
        $currentPage = $pagination['page'];
        $totalPages = $pagination['pageCount'];
        $nextPage = $pagination['nextPage'];
        $prevPage = $pagination['prevPage'];
        $pageInput = $pagination['pageInput'];
        $startItem = $currentPage != 1 ? ($pagination['current'] * $currentPage) - $pagination['current'] : ($currentPage - 1) * $pagination['current'] + 1;
        $endItem = $pagination['current'] * $currentPage;
    }
    // Build URL parameters for pagination
    unset($urlParams['url'], $urlParams['ext']);
    $paramString = !empty($urlParams) ? '&' . http_build_query($urlParams) : '';
?>
<div class="hstack" style="display: none" data-listing-nav="true" id="top-bar-nav-section-new">
    <?php if(!IS_MOBILE){ ?>
            <div class="listing-count" data-lc-visible="false" data-lc-total="4"><?=$startItem; ?> -  <?=$endItem; ?> <?= __('of'); ?> <?=$pagination['count']; ?></div>
    <?php } ?>
    <div class="input-group listing-jump">
        <a class="btn btn-secondary <?php echo IS_MOBILE ? ' btn-touch' : ''; ?>" onclick="initPageLoader()"
            id="top-nav-prev" href=""
            <?=!$prevPage ? 'disabled' : '' ?>
            title="<?php echo __('Previous Page', true) ?> (<?= $is_arabic ? 'Right Arrow' : 'Left Arrow' ?>)"
            data-bs-placement="bottom"
            data-bs-title="<?php echo __('Previous Page', true) ?> (<?= $is_arabic ? 'Right Arrow' : 'Left Arrow' ?>)"
            data-bs-toggle="tooltip">
            <i class="mdi mdi-chevron-left"></i>
        </a>
        <?php if(!IS_MOBILE){ ?>
        <!-- <input type="number" class="form-control" placeholder="Page 2 of 3" title="Jump to Page" data-bs-placement="bottom" data-bs-title="Jump to Page" data-bs-toggle="tooltip" data-lj-name="page" min="1" max="3" data-lj-current="2" data-lj-params="&type=1&name=omar" /> -->
        <input type="number" class="form-control"
            <?= $totalPages == 1 ? 'disabled' : '' ?>
            placeholder="<?php echo __('Page', true) . ' ' . $currentPage . ' ' . __('of', true) . ' ' . $totalPages ?>"
            title="<?php echo __('Jump to Page', true) ?>" data-placement="bottom"
            data-bs-title="<?php echo __('Jump to Page', true) ?>" data-toggle="tooltip" data-lj-name="page" min="1"
            max="<?php echo $totalPages ?>" data-lj-current="<?php echo $currentPage ?>"
            data-lj-params="<?php echo htmlspecialchars($paramString) ?>" />
        <?php } ?>
        <a class="btn btn-secondary <?php echo IS_MOBILE ? ' btn-touch' : ''; ?>" id="top-nav-next" href=""
            <?=!$nextPage ? 'disabled' : '' ?>
            title="<?php echo __('Next Page', true) ?> (<?= $is_arabic ? 'Left Arrow' : 'Right Arrow' ?>)"
            data-bs-placement="bottom"
            data-bs-title="<?php echo __('Next Page', true) ?> (<?= $is_arabic ? 'Left Arrow' : 'Right Arrow' ?>)"
            data-bs-toggle="tooltip">
            <i class="mdi mdi-chevron-right"></i>
        </a>
    </div>
</div>


<!-- Start pagination iframe -->
<!--  otherwise the styles will show the pagination btns even if there are n  -->
<script>

$('[data-lj-params]').on('focus', function () {
    $(this).val(Number(this.dataset.ljCurrent));
    $(this).select();
});

$('[data-lj-params]').on('blur', function () {
    $(this).val('');
});

$('[data-lj-params]').on('keydown', function (e) {
    if (e.key === 'Enter' || e.keyCode === 13) {
        e.preventDefault();

        const paramName = this.dataset.ljName; // should be 'page'
        const paramValue = this.value;
        const max = Number(this.max);
        const min = Number(this.min);

        if (paramValue > max || paramValue < min) {
            alert(APP.UTILS.TEXT.sprintf(__('This value should be between %s and %s.'), [min, max]));
            return;
        }

        const currentUrl = new URL(window.location.href);
        let path = currentUrl.pathname; // e.g., /owner/invoices/index/page:3
        const searchParams = new URLSearchParams(currentUrl.search);

        // Remove query param with same name, in case it was used previously
        searchParams.delete(paramName);

        // Replace or append the /page:X segment in the path
        const pageRegex = new RegExp(`/${paramName}:\\d+`);
        if (pageRegex.test(path)) {
            path = path.replace(pageRegex, `/${paramName}:${paramValue}`);
        } else {
            path += `/${paramName}:${paramValue}`;
        }

        // Parse any extra ljParams (e.g., &reset=1)
        const extraParams = new URLSearchParams(this.dataset.ljParams || '');
        for (const [key, val] of extraParams.entries()) {
            searchParams.set(key, val);
        }

        // Preserve iframe if already in current URL
        if (currentUrl.searchParams.has('iframe')) {
            searchParams.set('iframe', '1');
        }

        const finalUrl = path + (searchParams.toString() ? '?' + searchParams.toString() : '');

        // Redirect
        if (window.location.href.includes('iframe')) {
            window.location.href = finalUrl;
        } else {
            window.top.location.href = finalUrl;
        }
    }
});



</script>
<?php  if($this->params['url']['box'] && $this->params['paging'][$this->viewVars['modelName']]['count']  > 20)  : ?>
<style>
#top-bar-nav-section-new {
    background: #f6f9fc;
    color: #3a3e63;
    font-size: 16px;
    padding: 10px 15px;
    width: 100%;
    display: flex !important;
    align-items: center;
    flex-direction: row-reverse;
    justify-content: flex-start;
}

#top-bar-nav-section-new .btn-group {
    position: relative;
    display: inline-block;
    vertical-align: middle;
}

#top-bar-nav-section-new .btn-group .btn-default {
    font-size: 14px;
    padding: 10px 15px;
    background: #dbe7f3;
    border: 0;
    color: #3a3e63 !important;
    transition: all .2s ease-in-out;
}

#top-bar-nav-section-new .btn-group .btn-default.disabled {
    opacity: .65;
    background-color: #dbe7f3;
    border-color: #dbe7f3;
    color: #b7b7b7 !important;
}

.ltr #top-bar-nav-section-new #pagin-desc {
    padding-left: 0;
    padding-right: 15px;
}

.rtl #top-bar-nav-section-new #pagin-desc {
    padding-left: 15px;
    padding-right: 0;
}

#top-bar-nav-section-new #pagin-desc {
    font-size: 14px;
    height: 34px;
    line-height: 35px;
    font-weight: 700;
    color: #75799d;

}
</style>
<?php endif ?>
<!-- End pagination iframe -->