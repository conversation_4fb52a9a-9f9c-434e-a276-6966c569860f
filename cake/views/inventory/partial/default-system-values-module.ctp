<div class="setting-section" id="section5">
    <h3 class="fs-13 text-black mb-4 p-0"><?= __('Default System Values', true) ?></h3>
    <p><?= __('Define the default values applied to warehouses, products, and tax behavior.', true) ?></p>


    <div class="">


    <div class="form-group mt-12 mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black" for="">
                <?= __('Default Journal Account', true) ?>
            </label>
            <div class="d-flex gap-5 flex-column flex-md-row">
                    <?php
                    echo $this->element('journal_accounts_advanced_search_new', array('options' =>
                        array(
                            'input_name' => "data[".InventoryPlugin."][stock_transactions_default_journal_account_id]",
                            'id' => "StockTransactionDefaultJournalAccountId",
                            'value' => $this->data[InventoryPlugin]['stock_transactions_default_journal_account_id'],
                            'empty' => __('Please Select', true)
                    )));
            ?>               
            </div>
            <div class="form-text fs-7 mt-5">
                <?= __('Choose the subaccount affected by inventory-related stock movements for the current branch. By default, the system uses "Other Receivables" when dispensing and "Other Payables" when adding inventory.', true); ?>
            </div>
        </div>


        <!-- <div class="col-md-6">
            <label><?php __("Journal Account"); ?></label>
            <?php
                    // echo $this->element('journal_accounts_advanced_search', array('options' =>
                    //     array(
                    //         'input_name' => "data[".InventoryPlugin."][stock_transactions_default_journal_account_id]",
                    //         'id' => "StockTransactionDefaultJournalAccountId",
                    //         'value' => $this->data[InventoryPlugin]['stock_transactions_default_journal_account_id'],
                    //         'empty' => __('Please Select', true)
                    // )));
            ?>
        </div> -->



         <div class="form-group mt-12 mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black" for="72DefaultWarehouseForBranch">
                <?= __('Default Warehouse', true) ?>
            </label>
            <div class="d-flex gap-5 flex-column flex-md-row">
                <div class="flex-grow-1">
                    <?php echo $form->input('default_warehouse_for_branch', array('div' => false, 'options' => ['0' => __('Select Warehouse', true)] + $active_warehouses, 'class' => 'form-control form-control-selectize', 'label' => false )); ?>
                </div>
                <div class="flex-grow-0">
                    <?php echo '<a href="'.Router::url(['controller'=>'stores','action'=>'index']).'" target="_blank" class="btn btn-secondary text-decoration-none w-full" ><i class="fa fa-cog me-4"></i>'. __("Manage Warehouses" , true ).'<i class="mdi mdi-open-in-new ms-4"></i></a>'; ?>
                </div>
            </div>
            <div class="form-text fs-7 mt-5">
                <?= sprintf(__('Set a default warehouse for the current branch and all its assigned employees. Assign a default %s to override this setting and set their primary warehouse.', true), '<strong><a href="'.Router::url(['controller'=>'roles','action'=>'index']).'/?reset=1" target="_blank">' . __('warehouse per employee', true) . '</a></strong>') ?>
            </div>
        </div>

        <!-- <div class="col-md-6">
            <?php //echo $form->input('default_warehouse_for_branch', array('div' => '', 'options' => ['0' => __('Please Select', true)] + $active_warehouses, 'class' => 'form-control form-control-selectize', 'label' => __('Default Warehouse', true))); ?>
        </div> -->


        <div class="form-group mt-12 mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black" for="72DefaultWarehouseForBranch">
                <?= __('Default Price List', true) ?>
            </label>
            <div class="d-flex gap-5 flex-column flex-md-row">
                <div class="flex-grow-1">
                    <?php echo $this->element('price_list', ['label' => false, 'empty' => __('Please Select', true), 'width' => '100%' ,'input_name' => "data[".InventoryPlugin."][default_price_list]", 'option' => $this->data[InventoryPlugin]['default_price_list'] ]); ?>
                </div>
                <div class="flex-grow-0">
                    <?php echo '<a href="/v2/owner/price_lists" target="_blank" class="btn btn-secondary text-decoration-none w-full" ><i class="fa fa-cog me-4"></i>'. __("Manage Price Lists" , true ).'<i class="mdi mdi-open-in-new ms-4"></i></a>'; ?>
                </div>
            </div>
            <div class="form-text fs-7 mt-5">
                <?= __('Set a default price list to override product pricing in sales invoices for the current branch. This only applies to clients who don’t have an assigned price list.', true) ?>
            </div>
        </div>


        <!-- <div class="col-md-6">
            <label><?php __("Default Price List"); ?></label>
            <?php //echo $this->element('price_list', ['label' => false, 'empty' => __('Please Select', true), 'width' => '100%' ,'input_name' => "data[".InventoryPlugin."][default_price_list]", 'selected_option' => $this->data[InventoryPlugin]['default_price_list'] ]); ?>
        </div> -->

        <div class="form-group mt-12 mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black" for="72DefaultWarehouseForBranch">
                <?= __('Default Price List', true) ?>
            </label>
            <div class="d-flex gap-5 flex-column flex-md-row">
                <div class="flex-grow-1">
                    <?php echo $this->element('price_list', ['label' => false, 'empty' => __('Please Select', true), 'width' => '100%' ,'input_name' => "data[".InventoryPlugin."][default_price_list]", 'option' => $this->data[InventoryPlugin]['default_price_list'] ]); ?>
                </div>
                <div class="flex-grow-0">
                    <?php echo '<a href="/v2/owner/price_lists" target="_blank" class="btn btn-secondary text-decoration-none w-full" ><i class="fa fa-cog me-4"></i>'. __("Manage Price Lists" , true ).'<i class="mdi mdi-open-in-new ms-4"></i></a>'; ?>
                </div>
            </div>
            <div class="form-text fs-7 mt-5">
                <?= __('Set a default price list to override product pricing in sales invoices for the current branch. This only applies to clients who don’t have an assigned price list.', true) ?>
            </div>
        </div>

         <div class="form-group mt-12 mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black" for="">
                <?= __('Default Tax 1', true) ?>
            </label>
            <div class="d-flex gap-5 flex-column flex-md-row">
                <div class="flex-grow-1">
                    
                </div>
                <div class="flex-grow-0">
                    <?php echo '<button type="button" tabindex="-1" class="btn btn-secondary text-decoration-none" id="taxSettingsBtn" href="#" title="Auto Number Settings"><i class="fa fa-cog me-4"></i>  ' . __('Manage Taxes', true) . ' </button>'; ?>
                </div>
            </div>
            <div class="form-text fs-7 mt-5">
                <?= __('Assign a default tax for all newly added products to the current branch. You can adjust it per product later. Sales and purchase taxes can be set separately.', true) ?>
            </div>
        </div>

        <div class="form-group mt-12 mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black" for="">
                <?= __('Default Tax 2', true) ?>
            </label>
            <div class="d-flex gap-5 flex-column flex-md-row">
                <div class="flex-grow-1">
                    
                </div>
                <div class="flex-grow-0">
                    <?php echo '<button type="button" tabindex="-1" class="btn btn-secondary text-decoration-none" id="taxSettingsBtn" href="#" title="Auto Number Settings"><i class="fa fa-cog me-4"></i>  ' . __('Manage Taxes', true) . ' </button>'; ?>
                </div>
            </div>
            <div class="form-text fs-7 mt-5">
                <?= __('Assign a second default tax for newly added products to the current branch. You can modify it later and define separate values for sales and purchase taxes.', true) ?>
            </div>
        </div>


        <!--
        Change Note: New Type and lables
        -->
        <div class="form-group mt-12 mt-md-15 mb-0 bundles_div">
            <p class="form-label fs-8 fw-medium text-black">
                <?= __('Refund Calculation Method', true); ?></p>
            <div class="">
                <label class="form-check form-check-group py-10 form-check-custom">
                    <input class="form-check-input" type="radio" name="data[Site][bundle_type]" value="tax"
                        <?= empty($this->data['Site']['bundle_type']) || $this->data['Site']['bundle_type'] == 'tax' ? 'checked' : ''?> />
                    <span class="form-check-label fw-normal flex-column">
                        <p class="m-0 p-0 fs-8"><?= __('Based on the Sales Time Average Cost', true) ?></p>
                        <div class="form-text fs-7 mt-3">
                            <?= sprintf(__("Use the average cost at the time of the sales invoice.", true)) ?>
                        </div>
                    </span>
                </label>

                <label class="form-check form-check-group py-10 form-check-custom">
                    <input class="form-check-input" type="radio" name="data[Site][bundle_type]" value="without"
                        <?= $this->data['Site']['bundle_type'] == 'without' ? 'checked' : '' ?> />
                    <span class="form-check-label fw-normal flex-column">
                        <p class="m-0 p-0 fs-8"><?= __('Based on the Latest Average Cost', true) ?></p>
                        <div class="form-text fs-7 mt-3">
                            <?= __('Use the most recent average cost at the time of refund.', true) ?>
                        </div>
                    </span>
                </label>
            </div>
            <div class="form-text fs-7 mt-5">
                <?= sprintf(__("Define how refund prices are calculated, using either the average item cost at the sales time or the most recent average cost at the refund time. The average cost is auto-calculated from cumulative purchase invoice prices. Applies across all branches and can be adjusted manually.", true)); ?>
            </div>
        </div>
        <!-- <div class="col-md-6">
            <?php //echo $form->input(Settings::REFUND_RECEIPT_CALCULATION_METHOD, array('div' => '', 'options' => $form_data['refund_receipt_calculation_method']['options'], 'class' => 'selectpicker form-control', 'label' => __('Refund Receipt Cost Calculation Method', true) . '<span class="tooltip" title="refund-cost-calculation-based-on-average-cost"></span>')); ?>
        </div> -->




    </div>
</div>