<div class="side-nav-container compat">
    <div class="no-padding side-nav-new">

        <ul class="p-0" id="myTabs">
            <?php foreach ($sideMenuItems as $i => $sideMenuItem) {
                if (!$sideMenuItem['showIf']) {
                    continue;
                }
                ?>
                <li>
                    <a href="#section<?= ++$i ?>" class="active">
                        <i class="side-nav-new-icon <?= $sideMenuItem['icon'] ?>"></i>
                        <div class="side-nav-desc">
                            <h4><?= __($sideMenuItem['title'], true); ?></h4>
                            <p><?= __($sideMenuItem['description'], true); ?></p>
                        </div>
                    </a>
                </li>
            <?php } ?>
        </ul>
    </div>
</div>
<script>

    $(document).ready(function () {
        const $tabs = $('#myTabs a');

        // Build an array of jQuery section elements from the hrefs (skip missing)
        function buildSections() {
            return $tabs.map(function () {
                const sel = $(this).attr('href');
                const $el = $(sel);
                return $el.length ? $el : null;
            }).get().filter(Boolean).map($ => $); // returns array of jQuery objects
        }

        let sections = buildSections();

        // Rebuild on resize in case layout or anchors change
        $(window).on('resize', function () {
            sections = buildSections();
        });

        // Click behavior: scroll and set active immediately
        $tabs.on('click', function (e) {
            e.preventDefault();
            const target = $(this).attr('href');
            // Set active class on click
            $tabs.removeClass('active');
            $(this).addClass('active');

            // Animate scroll
            $('html, body').animate({
                scrollTop: $(target).offset().top - 150
            }, 500);
        });

        // Debounce helper
        function debounce(fn, wait) {
            let t;
            return function () {
                clearTimeout(t);
                t = setTimeout(() => fn.apply(this, arguments), wait);
            }
        }

        // Scroll handler: mark active based on section positions,
        // and force last when near bottom of page.
        function onScroll() {
            const scrollPos = $(document).scrollTop() + 180;
            let current = null;

            // find the last section whose top is <= scrollPos
            for (let i = 0; i < sections.length; i++) {
                const $sec = sections[i];
                if ($sec.offset().top <= scrollPos) {
                    current = $sec;
                }
            }

            // If near bottom, make last section active (fixes tiny last section)
            const nearBottom = (window.innerHeight + window.pageYOffset) >= (document.body.offsetHeight - 5);
            if (nearBottom && sections.length) {
                current = sections[sections.length - 1];
            }

            if (current && current.length) {
                const id = current.attr('id');
                if (id) {
                    $tabs.removeClass('active');
                    $tabs.filter('[href="#' + id + '"]').addClass('active');
                }
            } else {
                // nothing matched — remove active (optional)
                // $tabs.removeClass('active');
            }
        }

        // Use a small debounce to avoid handling scroll too many times
        $(window).on('scroll', debounce(onScroll, 50));

        // run once on load to set initial state
        onScroll();
    });

</script>