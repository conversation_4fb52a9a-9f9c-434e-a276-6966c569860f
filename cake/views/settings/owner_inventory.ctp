<?php echo $html->css('nav-tabs',false);

$breadcrumbs = [
    ['link' => '/v2/owner/inventory/settings', 'title' => __("Inventory", true )],
    ['link' => '#' ,'title' => __("Settings" , true )],
];
echo $this->element ('breadcrumbs' , ['breadcrumbs' =>array_values ( $breadcrumbs ) ]);?>
<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/create/create.min.css?v=".CSS_VERSION, null, []); ?>
<style>
#flashMessage {
    margin-inline-start: 300px;
}

@media (max-width: 768px) {
    #flashMessage {
        margin-inline-start: 60px;
    }
}

.modal .modal-dialog {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    min-height: calc(100% - (10px* 2));
    padding-top: 0px !important;
}

.modal .modal-content {
    width: 100%;
}

@media (min-width: 768px) {
    .modal .modal-dialog {
        min-height: calc(100% - (30px* 2));
    }
}
</style>
<div class="side-nav-content">
    <div class="compat">
        <div class="vstack gap-10">

            <?php
            echo $form->create(InventoryPlugin,array('id'=>'InvoiceForm', 'url'=>Router::url(array('action'=>'inventory'))));
            ?>

            <header class="pages-head page-fixed-start bg-none p-0" data-page-start="true">
                <div class="page-head add-page-head">
                    <div class="container container-full p-0">
                        <div class="page-head-row">
                            <div class="start">
                                <!-- left area -->
                                <div class="hstack">
                                    <h3 class="fs-8 text-black m-0 p-0 ms-10"><?= __('Products Settings', true) ?></h3>
                                </div>
                                <!-- /left area -->
                            </div>
                            <div class="center">
                                <!-- center area -->
                                <div class="hstack">

                                </div>
                                <!-- /center area -->
                            </div>
                            <div class="end p-0">
                                <!-- right area -->
                                <div class="hstack gap-0">

                                    <button type="button"
                                        class="btn btn-clear btn-responsive-icon btn-touch cancel-btn">
                                        <i class="mdi mdi-close-thick me-xl-4 d-xl-none"></i>
                                        <span><?php __("Discard") ?></span>
                                    </button>

                                    <button type="submit"
                                        class="btn btn-success btn-responsive-icon add-new-btn btn-touch">
                                        <i class="mdi mdi-content-save me-xl-4"></i>
                                        <span><?php __("Save") ?></span>
                                    </button>

                                </div>
                                <!-- /right area -->
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <div class="card mb-10">
                <div class="card-body p-10 p-md-25">
                    <?php include('views/inventory/partial/products-module.ctp'); ?>
                </div>
            </div>

            <div class="card mb-10">
                <div class="card-body p-10 p-md-25">
                    <?php include('views/inventory/partial/products-tracking-module.ctp'); ?>
                </div>
            </div>         

            <div class="card mb-10">
                <div class="card-body p-10 p-md-25">
                    <?php include('views/inventory/partial/stocktaking-module.ctp'); ?>
                </div>
            </div>

            <div class="card mb-10">
                <div class="card-body p-10 p-md-25">
                    <?php include('views/inventory/partial/stock-requisitions-module.ctp'); ?>
                </div>
            </div>

            <div class="card mb-10">
                <div class="card-body p-10 p-md-25">
                    <?php include('views/inventory/partial/default-system-values-module.ctp'); ?>
                </div>
            </div>

            <?php include('views/inventory/old-data.ctp'); ?>
           
            <?php echo $form->end(); ?>
        </div>
    </div>
</div>
<?php include('views/inventory/partial/inventory-sidebar.ctp'); ?>

<?php
if (IS_PC) {
    echo $javascript->link(array('summernote/summernote.min'));
    echo $html->css(array('../js/summernote/summernote'), false, ['inline' => false]);
}
?>
<!-- Enable the tabs -->
<script type="text/javascript">
var initial_form_state = "";
setTimeout(function() {
    initial_form_state = $('#InvoiceForm').serialize();
}, 1000);

$('#InvoiceForm').submit(function() {
    if (!$(".html-invoice").is(":focus")) {
        initial_form_state = $('#InvoiceForm').serialize();
    }

});

$(window).bind('beforeunload', function(e) {
    var form_state = $('#InvoiceForm').serialize();
    if (initial_form_state != form_state && initial_form_state != "") {

        var message = __(
            "You have unsaved changes on this page. Do you want to leave this page and discard your changes or stay on this page ?"
        );
        e.returnValue = message; // Cross-browser compatibility (src: MDN)
        return message;
    }
});

$(document).ready(function() {

    $(document).ready(function() {
        var $input = $('.form-control-selectize');
        $input.selectize(window.APP.VENDORS.DEFAULTS.selectize.single);
    });

    $("#multi_unit_check").change(function() {
        if ($(this).prop('checked')) {
            $('#Enable_Manage_Units').show();
        } else {
            $('#Enable_Manage_Units').hide();
        }
    });

    $("#72EnableStockRequests").change(function() {
        if ($(this).prop('checked')) {
            $('#EnableStockRequestsContent').show();
        } else {
            $('#EnableStockRequestsContent').hide();
        }
    });
    $("#72EnableRequisitions").change(function() {
        if ($(this).prop('checked')) {
            $('#Enable_Requisitions').show();
        } else {
            $('#Enable_Requisitions').hide();
        }
    });
    $("#72EnableRequisitionsPo").change(function() {
        if ($(this).prop('checked')) {
            $('#Enable_Requisitions_inbound').show();
        } else {
            $('#Enable_Requisitions_inbound').hide();
        }
    });
    


    $('body').on('change', '#72ShippingBilling', function() {

        if ($(this).val() == '0') {
            $('#custom_address').hide();
        } else {
            $('#custom_address').show();
        }
    });

    $("#multi_unit_check").change(function() {
        if ($(this).prop('checked')) {
            $(".units_div").show()
        } else {
            $(".units_div").hide()
        }
    })
    $("#bundle_check").change(function() {
        if ($(this).prop('checked')) {
            $(".bundles_div").show()
        } else {
            $(".bundles_div").hide()
        }
    })

    $("#enable_show_both_on_hand_and_available_stock_of_products").change(function() {
        if ($(this).prop('checked')) {
            $(".enable_show_both_on_hand_and_available_stock_of_products").show();
        } else {
            $('#validate_stock_of_products_by_available option:first-child').prop('selected',
                'selected').change();
            $(".enable_show_both_on_hand_and_available_stock_of_products").hide();
        }
    })
    $("#enable_show_both_on_hand_and_available_stock_of_products").change()

    $("#multi_unit_check").change()
    $("#bundle_check").change()
    $('#72ShippingBilling').trigger('change');

    $('#72PoBillingAddress,#72PoShippingAddress').summernote({
        toolbar: [
            ['style', ['bold', 'italic', 'underline', 'clear']],
            ['font', ['strikethrough']],
            ['fontsize', ['fontsize']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']]
        ]
    });

    $('#myTabs a').click(function(e) {
        if ($(this).attr('href').indexOf("#") != -1) {
            e.preventDefault();
            $(this).tab('show')
            if (history.pushState) {
                history.pushState(null, null, $(this).attr('href'));
            } else {
                location.hash = $(this).attr('href');
            }
        }

    });

    if ("onhashchange" in window) {
        function locationHashChanged() {
            $('a[href="' + location.hash + '"]').tab('show');
        }
        window.onhashchange = locationHashChanged;
    }


    if (location.hash !== '') $('a[href="' + location.hash + '"]').tab('show');
    $allow_negative_tracking = $('#allow_negative_tracking');
    $('#72EnableTrackingNumbers').on('change', function() {
        if (!$(this).attr('checked')) {
            $allow_negative_tracking.removeAttr('checked');
            $allow_negative_tracking.attr('disabled', 'disabled');
        } else {
            $allow_negative_tracking.removeAttr('disabled');
        }
    });
    $('#72DisableOverdraft').on('change', function() {
        if (!$(this).attr('checked')) {
            $allow_negative_tracking.removeAttr('checked');
            $allow_negative_tracking.attr('disabled', 'disabled');
        } else {
            $allow_negative_tracking.removeAttr('disabled');
        }
    });
    $('#72EnableTrackingNumbers').change();
    $('#72DisableOverdraft').change();
});
</script>

<script>
$(document).ready(function() {

    $(document).on('click', '#autoNumberSettingsBtn', function() {
        IzamModal.closeModals();

        // Build URL with multiple query params
        const srcUrl =
            '<?= Router::url(['controller' => 'settings', 'action' => 'numbering', AutoNumber::TYPE_PRODUCT]) ?>?box=1&frameModal=1';

        IzamModal.addHtmlModal(
            '<iframe style="width:100%; height:100%; border:none;" ' +
            'src="' + srcUrl + '"></iframe>',
            '',
            ''
        );
    });

    $(document).on('click', '#autoNumberSettingsStocktaking', function() {
        IzamModal.closeModals();

        // Build URL with multiple query params
        const srcUrl =
            '<?= Router::url(['controller' => 'settings', 'action' => 'numbering', AutoNumber::TYPE_STOCKTAKING]) ?>?box=1&frameModal=1';

        IzamModal.addHtmlModal(
            '<iframe style="width:100%; height:100%; border:none;" ' +
            'src="' + srcUrl + '"></iframe>',
            '',
            ''
        );
    });

    $(document).on('click', '#autoNumberSettingsRequisition', function() {
        IzamModal.closeModals();

        // Build URL with multiple query params
        const srcUrl =
            '<?= Router::url(['controller' => 'settings', 'action' => 'numbering', AutoNumber::TYPE_REQUISITION_TRANSFER]) ?>?box=1&frameModal=1';

        IzamModal.addHtmlModal(
            '<iframe style="width:100%; height:100%; border:none;" ' +
            'src="' + srcUrl + '"></iframe>',
            '',
            ''
        );
    });

    $(document).on('click', '#autoNumberSettingsOutboundRequisition', function() {
        IzamModal.closeModals();

        // Build URL with multiple query params
        const srcUrl =
            '<?= Router::url(['controller' => 'settings', 'action' => 'numbering', AutoNumber::TYPE_REQUISITION_OUTBOUND]) ?>?box=1&frameModal=1';

        IzamModal.addHtmlModal(
            '<iframe style="width:100%; height:100%; border:none;" ' +
            'src="' + srcUrl + '"></iframe>',
            '',
            ''
        );
    });

    $(document).on('click', '#autoNumberSettingsInboundRequisition', function() {
        IzamModal.closeModals();

        // Build URL with multiple query params
        const srcUrl =
            '<?= Router::url(['controller' => 'settings', 'action' => 'numbering', AutoNumber::TYPE_REQUISITION_INBOUND]) ?>?box=1&frameModal=1';

        IzamModal.addHtmlModal(
            '<iframe style="width:100%; height:100%; border:none;" ' +
            'src="' + srcUrl + '"></iframe>',
            '',
            ''
        );
    });

    $(document).on('click','#taxSettingsBtn', function() {
        IzamModal.closeModals();
        IzamModal.addHtmlModal(
            '<iframe style="width:100%; height:100%; border:none;" src="<?= Router::url(['controller'=>'taxes','action'=>'index']) ?>?reset=1&box=1&frameModal=1"></iframe>',
            '',
            '<?= __('Manage Taxes', true); ?>',
            true
        );
    });

    


});

window.addEventListener('message', function(ev) {
    const data = ev.data || {};
    if (data.action === 'closeModalFromIframe') {
        if (window.IzamModal && typeof window.IzamModal.closeModals === 'function') {
            window.IzamModal.closeModals();
        }


        if (data.next_number && data.numbering_type) {
            if (data.numbering_type == '18') {
                console.log('Received numbering_type:', data.numbering_type);

                $('input[name="data[72][next_product_number]"]').val(data.next_number);
            } else if (data.numbering_type == <?= AutoNumber::TYPE_STOCKTAKING ?>) {
                $('input[name="data[72][next_stock_taking_number]"]').val(data.next_number);

            } else if (data.numbering_type == <?= AutoNumber::TYPE_REQUISITION_TRANSFER ?>) {
                $('input[name="data[72][next_requisition_number]"]').val(data.next_number);

            } else if (data.numbering_type == <?= AutoNumber::TYPE_REQUISITION_OUTBOUND ?>) {
                $('input[name="data[72][next_requisition_outbound_number]"]').val(data.next_number);

            } else if (data.numbering_type == <?= AutoNumber::TYPE_REQUISITION_INBOUND ?>) {
                $('input[name="data[72][next_requisition_inbound_number]"]').val(data.next_number);

            }
            
        }
    }
});
</script>


<?php
echo $javascript->link(array('report_sideMenu.js'));
 ?>