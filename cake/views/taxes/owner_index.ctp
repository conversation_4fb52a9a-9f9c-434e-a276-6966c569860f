<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/index/index.min.css?v=".CSS_VERSION, null, []); ?>
<?php echo $html->css(CDN_ASSETS_URL ."s2020/css/create/create.min.css?v=".CSS_VERSION, null, []); ?>
<?php echo $javascript->link(array('jquery.are-you-sure.js')); ?>
<?php $delete_image_url = Router::url('/css/images/action-icons/close.png'); ?>
<?php
$url = array('action' => 'update');
if (isset($_GET['box'])) {
    echo $session->flash();
    $url['?'] = array('box' => 1);
}
?>
<form action="<?php echo Router::url($url) ?>" method="post" id="TaxesForm">
    <div  id="RelatedTaxes" class="table-responsive" data-sticky-table-header="false">
        <table class="table table-small-font table-striped  taxs-table" cellspacing="0" cellpadding="0">
            <thead>
                <tr>
                    <th data-priority="1"><?php __("Tax Name") ?></th>
                    <th data-priority="1"><?php __("Tax Value") ?></th>
                    <th data-priority="1"><?php __("Included") ?> <span class="tooltip" title="tax-included"></span></th>
                    <th data-priority="2"></th>
                </tr>
            </thead>
            <tbody>
                <?php $tax_counter = 0; ?>
                <?php
                debug($taxes);
                if (!empty($taxes)):
                    ?>
    <?php foreach ($taxes as $i => $tax): ?>
                        <tr class="tax-row">
                            <td class="td_editable">
                                <?php
                                echo $form->input('Tax.' . $i . '.id', array('class' => 'INPUT form-control ', 'value' => $tax['Tax']['id']));
                                echo $form->input('Tax.' . $i . '.name', array('class' => 'INPUT TaxName form-control', 'value' => $tax['Tax']['name'], 'label' => false, 'div' => 'form-group input mb-0 text'));
                                ?>
                            </td>
                            <td class="td_editable">
                                <?php
                                echo $form->input('Tax.' . $i . '.value', array('class' => 'INPUT TaxValue form-control', 'value' => $tax['Tax']['value'], 'after' => '<span class="input-group-addon px-2 border-0 bg-transparent s2020">%</span>', 'label' => false, 'div' => 'form-group mb-0 input-group h-100'));
                                ?>
                            </td>
                            <td class="td_editable">
                                <?php
                                //echo $form->input('Tax.' . $i . '.included', array('class' => 'INPUT TaxIncluded ', 'type' => 'checkbox', 'value' => $tax['Tax']['included'], 'checked' => $tax['Tax']['included'], 'label' => "&nbsp;", 'div' => 'clip-check check-success'));
                                echo $form->select('Tax.' . $i . '.included', ['0' =>  __("Exclusive" , true ), "1"=> __("Inclusive" , true )], array( 'empty' => false , 'class' => 'INPUT TaxIncluded ', 'type' => 'checkbox', 'value' => $tax['Tax']['included'], 'checked' => $tax['Tax']['included'], 'label' => "&nbsp;", 'div' => 'clip-check check-success'));
                                ?>
                            </td>
                            <td class="delete-product-cell">
                                <a href="#" class="removeTax delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020"><i class="s2020 far fa-minus-circle"></i></a>
                            </td>
                        <?php $tax_counter++; ?>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
    <?php $i = 0; ?>
                    <tr class="tax-row">
                        <td class="td_editable">
                            <?php
                            echo $form->input('Tax.' . $i . '.id', array('class' => 'INPUT form-control '));
                            echo $form->input('Tax.' . $i . '.name', array('class' => 'INPUT TaxName form-control ', 'label' => false, 'div' => 'form-group input mb-0 text'));
                            ?>
                        </td>
                        <td class="td_editable">
                            <?php
                            echo $form->input('Tax.' . $i . '.value', array('class' => 'INPUT TaxValue form-control', 'after' => '<span class="input-group-addon px-2 border-0 bg-transparent s2020">%</span>', 'label' => false, 'div' => 'form-group mb-0 input-group h-100'));
                            ?>
                        </td>
                        <td class="td_editable">
                            <?php //echo $form->input('Tax.' . $i . '.included', array('class' => 'INPUT TaxIncluded ', 'type' => 'checkbox', 'value' => $tax['Tax']['included'], 'checked' => $tax['Tax']['included'], 'label' => "&nbsp;", 'div' => 'clip-check check-success'));
                                echo $form->select('Tax.' . $i . '.included', ['0' =>  __("Exclusive" , true ), "1"=> __("Inclusive" , true )], array( 'empty' => false , 'class' => 'INPUT TaxIncluded ', 'type' => 'checkbox', 'value' => $tax['Tax']['included'], 'checked' => $tax['Tax']['included'], 'label' => "&nbsp;", 'div' => 'clip-check check-success'));
                            ?>
                        </td>
                        <td class="delete-product-cell">
                            <a href="#" class="removeTax delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020"><i class="s2020 far fa-minus-circle"></i></a>
                        </td>
                    <?php $tax_counter++; ?>
                    </tr>
<?php endif; ?>
            </tbody>
        </table>
        <div class="new-row mt-0">
            <a class="AddItem add-row m-b-xs my-0 fs-14 btn-s2020 btn-light-s2020 text-dark-blue s2020 font-weight-bold" id="AddTax" href="#"><i class="s2020 far fa-plus-circle text-primary mr-2"></i><?php __("Add") ?></a>
        </div>
    </div>
    <div class="clear"></div>
    
    <div class="compat" id="modalBtns" style="display:none;">
        <div class="d-flex p-10 border-top gap-6 hstack justify-content-center position-fixed bottom-0 w-100 bg-white">
            <div class="flex-grow-1">
                <button type="button"  class="btn btn-secondary py-8 w-100" data-bs-dismiss="modal"><?php __("Discard") ?></button>
            </div>
            <div class="flex-grow-1">
                <button type="submit" class="btn btn-success py-8 w-100"><?php __("Save") ?></button>
            </div>
        </div>
    </div>
	<div class="pages-head-s2020">
		<div class="container">
			<div class="row-flex align-items-center">
				<div class="col-flex-sm-6">
					<div class="pages-head-title">
						<h2 class="pb-0"><?php __('Taxes'); ?></h2>
					</div>
				</div>
				<div class="col-flex-sm-6 d-flex justify-content-end">
					<button type="submit" class="btn s2020 btn-icn btn-success font-weight-medium ml-2">
						<i class="mdi mdi-content-save-outline fs-20"></i>
						<span><?php __("Save") ?></span>
					</button>
				</div>
			</div>
		</div>
	</div>
</form>
<?php
if (!empty($_GET['box'])) {
    echo $javascript->link('functions');
    echo $html->css(array('listing', 'buttons'), false, ['inline' => false]);
}
?>
<?php if (!empty($_GET['box']) && !empty($_GET['updateparent'])): ?>
    <script type="text/javascript">//<![CDATA[
        top.updateTaxes();
        top.CompareTax(true);
        //]]></script>
<?php endif; ?>
<script type="text/javascript">

    var newtax = '<tr class="tax-row"><td class="td_editable"><?php echo $javascript->escapeString($form->input('Tax.start.id', array('class' => 'INPUT form-control', 'value' => ''))) ?>';
    newtax += '<?php echo $javascript->escapeString($form->input('Tax.start.name', array('class' => 'TaxName INPUT form-control', 'label' => false, 'value' => '', 'div' => 'form-group input mb-0 text', 'error' => false))) ?></td>';
    newtax += '<td class="td_editable"><div class="input h-100 text"><?php echo $javascript->escapeString($form->input('Tax.start.value', array('class' => 'TaxValue INPUT form-control', 'div' => 'input-group h-100 form-group mb-0', 'label' => false, 'value' => '', 'after' => '<span class="input-group-addon px-2 border-0 bg-transparent s2020">%</span>', 'error' => false))) ?></div></td>';
    newtax += '<td class="td_editable"><?php echo $javascript->escapeString($form->select('Tax.start.included', ['0' =>  __("Exclusive" , true ), "1"=> __("Inclusive" , true )], array( 'empty' => false , 'class' => 'INPUT TaxIncluded ', 'type' => 'checkbox', 'value' => $tax['Tax']['included'], 'checked' => $tax['Tax']['included'], 'label' => "&nbsp;", 'div' => 'clip-check check-success'))) ?></td>';
    //newtax += '<td class="td_editable"><?php //echo $javascript->escapeString($form->input('Tax.start.included', array('class' => 'TaxIncluded INPUT  ', 'label' => false, 'type' => 'checkbox', 'value' => 0, "zzz" => 11, 'error' => false, 'div' => 'clip-check check-success', 'label' => '&nbsp;'))) ?></td>';
    newtax += '<td class="delete-product-cell"><?php echo $javascript->escapeString('<a href="#" class="removeTax delete-ico btn-s2020 btn-sm-s2020 fs-18 text-danger-s2020"><i class="s2020 far fa-minus-circle"></i></a>') ?></td></tr>';
    //	newtax += '<?php echo $javascript->escapeString('<div class="clear"></div>') ?>';
    var tax_counter = '<?php echo $tax_counter ?>';
    tax_counter++;
    

   
    $(function() {
        function appendTax() {
            if ($('#RelatedTaxes').is(':visible')) {
                $('#RelatedTaxes table').append(newtax.replace(/start/g, tax_counter));
                tax_counter++;
            } else {
                $('#RelatedTaxes').show();
            }

            return false;
        }
       $('#AddTax').click(appendTax);

        $('.removeTax').live('click', function() {
            if (confirm(__('Are you sure you want to delete?'))) {
                var container = $(this).parents('table');
                $(this).parents('tr').remove();
                if ($('.tax-row', container).length == 0) {
                    //appendTax();
                }
            }
            return false;
        });

        // -------------------------
        $('#TaxesForm').submit(function(evt) {
			$('.tax-row').each(function(){
				if($('.TaxName',this).val()==''&&$('.TaxValue',this).val()=='')
					$(this).remove();
			});
			
            if (validateFields()) {
                //				$(this).submit();
                return true;
            }

            return false;
        });

        //--------------------------
        function validateFields() {
			
            var rules = {
                '.TaxName': ['notEmpty'],
                '.TaxValue': {
                    'rules': ['isNumeric','notEmpty']
                }
            };
            var validationMessages = {
                notEmpty: __('Required'),
                isNumeric: __('Valid number required'),
                isNotMinus: __('Positive number required')
            };

            if (!validate(rules, validationMessages)) {
                $('.error-message').each(function( ) {
                    console.log($(this).parent('div').html());
                    $(this).insertAfter($(this).parent('div'));
                });
            } else {
                return true;
            }

        }
		if($('.tax-row').length==0){
			$('#RelatedTaxes table').append(newtax.replace(/start/g, tax_counter));
		}
		
		$('#TaxesForm').areYouSure({'message':'<?php str_replace("'","\\\'",__('Your entries have not been saved',true))?>'});
		
    
    });

    $(function() {
		const params = new URLSearchParams(window.location.search);

		if (params.get('frameModal') === '1') {
 			$('#modalBtns').show();
 			$('.pages-head-s2020').hide();
			$('#flashMessage').addClass('frameModal-space');
 		}
	});
    $(document).on('click', 'button[data-bs-dismiss="modal"]', function () {
        if (window.parent && window.parent.IzamModal) {
            window.parent.IzamModal.closeModals();
        }
    });
</script>
