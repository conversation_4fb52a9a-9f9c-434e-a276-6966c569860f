<?php
/*
 * params[$value the selected value]
 */
if(!isset($cats_list) || !empty($cats_list))
{
    $Cat = ClassRegistry::init('JournalCat');
	$conditions = [];
    $cats_list=$Cat->find('list');

}
$m_options=array( 'selected_field' => 'name','id' => 'journal_accounts','label' => false, 'class' => 'advanced_journal_account_id', 'empty' => __('Default Account', true),'width' => '100%', 'disabled' => $options['disabled'] ?? false,'input_name' => 'journal_account_id', 'value' => $value , 'apply_specify_branch' => $options['allow_sepecify'] ?? false ) ;
foreach ($cats_list as $k => $v) {
    $cats_list[$k] = __at($v);
}
if(isset($options)&& is_array($options)) $m_options= array_merge ($m_options, $options);
echo $this->element('advanced_search_new', array('controller' => 'journal_accounts','parents_list' => $cats_list,'parents' => true,'model' =>'JournalAccount','action' => 'json_find','parent_id' => /*warning suppress*/ $parent ?? '', 'input_options' => $m_options));

?>
<style>
    a.select-parent{
        pointer-events: none;
        background-color: #e4ebf2;
    }
</style>

 
