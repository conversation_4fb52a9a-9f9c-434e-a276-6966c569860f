<?php


    $Model = ClassRegistry::init($model);
	$ajax_records=false;
	$records_count=$Model->getRecordCount();
	$records=array();
	//warning suppress
	if(isset($ajax_records_count) && $records_count<$ajax_records_count) {
			$records=  $Model->getRecordsList(0);
	}
	else {
		if(!empty($input_options['value']))
		{
		$records= $Model->getRecordsList(array($model.'.id'=>$input_options['value']),30);
		$ajax_records=true;
		}
	}
	$options = array(
		//element options
	'controller' => isset($controller) ? "$controller" : "",
	'action' => isset($action) ? "$action" : "",
	'model' => isset($model) ? "$model" : "",
	'ajax_records_count' => isset($ajax_records_count) ? "$ajax_records_count" : AJAX_CLIENT_COUNT,
	'parents' => isset($parents) ? $parents : false ,
	'parents_list' => isset($parents_list) ? $parents_list: [],
		//end element options
		//input options
	'input_options' => array(
			'label' => isset($input_options['label']) ? $input_options['label'] : __('Client', true),
			'class' => isset($input_options['class']) ? $input_options['class'].' form-control form-control-selectize':'form-control form-control-selectize',
			'data-live-search' => 'true',
			'type' => 'select',
			'data-width' => !empty($input_options['width']) ? $input_options['width']: '100%',
			'div' => isset($input_options['div']) ? $input_options['div'] : false,
			'after' => isset($input_options['after']) ? $input_options['after'] : false,
			'before' => isset($input_options['before']) ? $input_options['before'] : false,
			'empty' => $input_options['empty'] ? $input_options['empty'] : __('Select Journal account', true),
			'name' => !empty($input_options['input_name']) ? $input_options['input_name'] : null,
			'options' => $records + ($input_options['options'] ?? []),
//			'multiple' => true ,
			'value' => !empty($input_options['value']) ? $input_options['value'] : null,
			'data-selected-text-format' => 'values',
			'id' => isset($input_options['id']) ? $input_options['id']:'',
			'disabled' => $input_options['disabled']??false,

		)

		//end input_options
);
	if(isset($input_options['multiple']) && $input_options['multiple'])
	{
		$options['input_options']['multiple'] = true ;
	}
    if(isset($input_options['queryParam']) && $input_options['queryParam'])
    {
        $queryParam = $input_options['queryParam'];
    } else {
        $queryParam = [];
    }

//	debug($options);
	echo $javascript->link(array('bootstrap-select_v'.JAVASCRIPT_VERSION.'.js'));
	echo $html->css(array('bootstrap-select_v'.CSS_VERSION.'.css'));

		$options['input_options']['class'].= ' with-ajax';
		echo $javascript->link(array('ajax-bootstrap-select.min.js'));
		echo $html->css(array( 'ajax-bootstrap-select'));
		?>

        <script type="text/javascript">
            if(typeof scriptLoaded == 'undefined'){
                var scriptLoader = true;

	$(function(){
	<?php //warning suppress ?>
	parent_id = '<?= $parent_id ?? '' ?>';
	apply_specify_branch = '<?= $input_options['apply_specify_branch']  ?>';
	
	 selectionParam = '.<?= $options['input_options']['class'] ?>';
	 selectionParam = selectionParam.split(' ');
	 selectionParam = selectionParam.join('.');
     selectionParam=selectionParam.replace('..','.');
    	 var emptyTitle= '<?php echo __($options['input_options']['empty'],true) ?>';
		 parents = '<?php echo $options['parents'] ?>' ;
		 model_ajax_options = {
        ajax          : {
            url     : '<?php echo Router::url(['controller'=>$options['controller'],'action'=>$options['action'], '?' => array_merge(['parent_type' => $_GET['parent_type']], $queryParam)]); ?>',
            type    : 'get',
            dataType: 'json',
            data    : function()
			{
				params  = {
                    <?= !empty($query_param) ? $query_param : 'q' ?>: '{{{q}}}'
				};
				if(parent_id != '')
					params.parent_id = parent_id;

				if(apply_specify_branch != ''){
					params.apply_specify_branch = apply_specify_branch;
				}

				return params;
			}

        },
        locale        : {
			statusSearching: '<?php __('Searching...') ?>',
            emptyTitle: '<?php echo __($options['input_options']['empty'],true) ?>',
			searchPlaceholder: '<?php !empty($placeholder) ? __($placeholder) : __('Enter Name, Code') ?>',

        },


        preprocessData: function (data) {

            var i, l = data.length, array = [];
			html = '';
			var parents_list = <?php echo json_encode($options['parents_list']) ?> ;
            if (l) {
				previous_parent_name = '';
                for (i = 0; i < l; i++) {
//					if(parents){
						if(data[i].details != "" && typeof data[i].details != 'undefined'){

						parent_name = '';
							cat_ids = data[i].details.split(',').reverse();
							j = 0;
							cat_ids.forEach(function(cat_id)
							{
                                if(cat_id == -1) {
                                    return;
                                }
								if(j == 0)
								parent_name += parents_list[cat_id] ;
							else parent_name += ' > ' + parents_list[cat_id]  ;
							j++ ;
							});
							if(previous_parent_name !== parent_name){
									array.push($.extend(true, {}, {
									text : parent_name,
									value: parent_name,
									class: 'select-parent',
									data : {
			//                            subtext: data[i].details
									}
								}));
							}
						previous_parent_name = parent_name ;
						}
//				}

                    array.push($.extend(true, data[i], {
                        text : data[i].name,
                        value: data[i].id,
						class: '',
                        data : {
//                            subtext: data[i].details
                        }
                    }));
                }
                <?php foreach ($input_options['options']??[] as  $key => $option) { ?>
                array.push($.extend(true, data[i], {
                    text : '<?php  echo $option ?>',
                    value: '<?php echo $key?>',
                    class: '',
                    data : {
//                            subtext: data[i].details
                    }
                }));
                <?php }  ?>
            }
            <?php
                foreach ($input_options['additional_options'] as $additional_option) {
                    ?>
                array.push({text: "<?= $additional_option["text"] ?>", value: "<?= $additional_option["value"] ?>"});
            <?php
                }
            ?>
            return array;
        }
    };


			$(selectionParam).selectpicker().filter('.with-ajax').ajaxSelectPicker(model_ajax_options);
		    $(selectionParam).append('<option value="">'+emptyTitle+'</option>').selectpicker('refresh');
            // $(selectionParam).trigger('change').data('AjaxBootstrapSelect').list.cache = {};
            $(selectionParam).trigger('change');
            $(selectionParam).data('model_ajax_options', model_ajax_options);

			$(selectionParam).on('change',function()
			{
				$('a.select-parent').each(function()
							{
								parentTitle = $(this).children('span.text').text();
								$(this).parent('li').replaceWith($('<li class="dropdown-header" data-original-index="null"><span class="text">'+ parentTitle +'</span></li>'))
							});
			});
			if(parents){

				$(document).ajaxComplete(function( event, xhr, settings ) {
					if ( settings.url.indexOf(model_ajax_options.ajax.url) ===  0) {
						setTimeout(function(){
							$('a.select-parent').each(function()
							{
								parentTitle = $(this).children('span.text').text();
								$(this).parent('li').replaceWith($('<li class="dropdown-header" data-original-index="null"><span class="text">'+ parentTitle +'</span></li>'))
							});

						},100);
					}
				  });
			  }
			  selectedValues = [];
			  selectedValue = "";
			  selectedName = "" ;

			  <?php foreach($records as $k => $v) { ?>
			  selectedValues.push({k: '<?= $k ?>', v: `<?= $v ?>`});

			  <?php  } ?>
        /**
         *
         * @param selectionParam
         * @param selectedValues
         */
                window.ajaxSelectPickerSetValue = function (selectionParam, selectedValues)
                {

                    if(selectedValues.length > 0 && selectedValues[0].k != ""){
                        selected = '<optgroup label="Currently Selected">';
                        selectedText = '';
                        selectedValues.forEach(function(el)
                        {
                            selectedText += el.v+',';
                            selected += '<option value="'+ el.k +'" selected="selected">' + el.v+ ' </option>'
                        });
                        selectedText = selectedText.substring(0,selectedText.length - 1 );
                        $(selectionParam).last().siblings('div.bootstrap-select').find('span.filter-option.pull-left').text(selectedText);
                        selected += '</optgroup>';
                    }
                }


        window.ajaxSelectPickerSetValue(selectionParam, selectedValues);

//

	});


function trim(stringToTrim) {
	return stringToTrim.replace(/^\s+|\s+$/g,"");
}
function ltrim(stringToTrim) {
	return stringToTrim.replace(/^\s+/,"");
}
function rtrim(stringToTrim) {
	return stringToTrim.replace(/\s+$/,"");
}
            }
	</script>
<?php

	echo $form->input(isset($options["input_options"]['name'])?$options["input_options"]['name']:'asc',$options['input_options']);

?>
